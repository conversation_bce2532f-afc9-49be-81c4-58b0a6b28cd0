/*
###
Description:    Batch add properties to download queue with flexible query conditions

Usage:

	# Process all documents without phoLH:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -dryrun"

	# Process specific IDs (bracket format):
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -id=[TRBC12248877,TRBC12248878,TRBC12248879] -dryrun"

	# Process documents with custom query (documents that have docLH field and it's not null):
	./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{docLH:{\$ne:null}}' -dryrun"

	# Process documents where phoUrls decompressed length differs from phoLH length:
	./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -phoNum -dryrun"

Create date:    2025-07-21
Author:         Maggie
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	"github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for processing")
	idFlag     = flag.String("id", "", "Property IDs to add to queue (optional, uses priority 50000). Format: id1,id2,id3 or [id1,id2,id3]")
	queryFlag  = flag.String("query", "", "Custom query conditions in JSON format (e.g., 'docLH:{$ne:null}' or '{docLH:{$ne:null}}' or '{\"docLH\":{\"$ne\":null}}') - quotes required to protect shell special characters")
	phoNumFlag = flag.Bool("phoNum", false, "Check phoUrls decompressed length vs phoLH length and add to queue if different")
	speedMeter *gospeedmeter.SpeedMeter
	startTime  = time.Now()
	queue      *goresodownload.ResourceDownloadQueue
)

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// preprocessQuery converts simplified query format to standard JSON
// Supports: {docLH:{$ne:null}} -> {"docLH":{"$ne":null}}
// Also supports: docLH:{$ne:null} -> {"docLH":{"$ne":null}}
func preprocessQuery(query string) string {
	// If it already looks like proper JSON (contains quotes), return as-is
	if strings.Contains(query, `"`) {
		return query
	}

	// Handle queries without outer braces (e.g., docLH:{$ne:null})
	result := strings.TrimSpace(query)
	if !strings.HasPrefix(result, "{") {
		result = "{" + result + "}"
	}

	// Simple replacements for common patterns
	// Replace common field names
	result = strings.ReplaceAll(result, "docLH:", `"docLH":`)
	result = strings.ReplaceAll(result, "phoLH:", `"phoLH":`)
	result = strings.ReplaceAll(result, "_id:", `"_id":`)

	// Replace MongoDB operators
	result = strings.ReplaceAll(result, "$ne:", `"$ne":`)
	result = strings.ReplaceAll(result, "$eq:", `"$eq":`)
	result = strings.ReplaceAll(result, "$in:", `"$in":`)
	result = strings.ReplaceAll(result, "$size:", `"$size":`)
	result = strings.ReplaceAll(result, "$exists:", `"$exists":`)
	result = strings.ReplaceAll(result, "$gt:", `"$gt":`)
	result = strings.ReplaceAll(result, "$lt:", `"$lt":`)
	result = strings.ReplaceAll(result, "$gte:", `"$gte":`)
	result = strings.ReplaceAll(result, "$lte:", `"$lte":`)
	result = strings.ReplaceAll(result, "$or:", `"$or":`)
	result = strings.ReplaceAll(result, "$and:", `"$and":`)

	// Handle special values - null should remain as null (not quoted)
	// No need to change :null to :"null" because null is a JSON literal

	return result
}

// shouldProcessForPhoNum checks if document should be processed based on phoUrls vs phoLH length comparison
func shouldProcessForPhoNum(doc bson.M, id string) (bool, error) {
	// Get phoUrls field
	phoUrls, hasPhoUrls := doc["phoUrls"]
	if !hasPhoUrls || phoUrls == nil {
		golog.Debug("Skipping document: no phoUrls field", "_id", id)
		speedMeter.Check("skipped_no_phoUrls", 1)
		return false, nil
	}

	// Get phoLH field
	phoLH, hasPhoLH := doc["phoLH"]
	var phoLHLength int
	if !hasPhoLH || phoLH == nil {
		phoLHLength = 0
	} else {
		// Calculate phoLH length
		if phoArray, ok := phoLH.(primitive.A); ok {
			phoLHLength = len(phoArray)
		} else if phoArray, ok := phoLH.([]interface{}); ok {
			phoLHLength = len(phoArray)
		} else {
			golog.Debug("Skipping document: phoLH is not an array", "_id", id, "phoLH_type", fmt.Sprintf("%T", phoLH))
			speedMeter.Check("skipped_invalid_phoLH", 1)
			return false, nil
		}
	}

	// Decompress phoUrls and get media array using gohelper
	mediaArray, err := gohelper.DecompressPhoUrls(phoUrls)
	if err != nil {
		golog.Error("Failed to decompress phoUrls", "_id", id, "error", err)
		speedMeter.Check("phoUrls_decompress_errors", 1)
		return false, fmt.Errorf("failed to decompress phoUrls: %w", err)
	}
	phoUrlsLength := len(mediaArray)

	// Compare lengths
	if phoUrlsLength != phoLHLength {
		if phoLHLength > 0 {
			golog.Info("Length mismatch found - adding to queue",
				"_id", id,
				"phoUrls_length", phoUrlsLength,
				"phoLH_length", phoLHLength)
			speedMeter.Check("length_mismatch_found", 1)
		} else {
			golog.Debug("no phoLH", "_id", id)
			speedMeter.Check("no_phoLH", 1)
		}
		return true, nil
	}

	// Lengths match - skip this document
	golog.Debug("Lengths match - skipping document",
		"_id", id,
		"phoUrls_length", phoUrlsLength,
		"phoLH_length", phoLHLength)
	speedMeter.Check("skipped_lengths_match", 1)
	return false, nil
}

// ProcessingMode represents different processing modes
type ProcessingMode int

const (
	ModeDefault  ProcessingMode = iota // Default mode: process documents without phoLH
	ModePhoNum                         // phoNum mode: check phoUrls vs phoLH length
	ModeSpecific                       // Specific ID or custom query mode
)

// getProcessingMode determines which processing mode to use
func getProcessingMode() ProcessingMode {
	if *phoNumFlag {
		return ModePhoNum
	}
	if *idFlag != "" || *queryFlag != "" {
		return ModeSpecific
	}
	return ModeDefault
}

// shouldProcessDocument determines if a document should be processed based on the mode
func shouldProcessDocument(doc bson.M, id string, mode ProcessingMode) (bool, error) {
	switch mode {
	case ModePhoNum:
		// For phoNum mode, check in cursor processing (not in query)
		return shouldProcessForPhoNum(doc, id)
	case ModeSpecific:
		// For specific ID or custom query, always process
		golog.Debug("Processing document: specific mode", "_id", id)
		return true, nil
	case ModeDefault:
		// For default mode, check phoLH field
		return shouldProcessForDefault(doc, id)
	default:
		return false, fmt.Errorf("unknown processing mode: %d", mode)
	}
}

// parseSpecificIDs parses the ID flag and returns a list of property IDs
func parseSpecificIDs() ([]string, error) {
	if *idFlag == "" {
		return nil, nil
	}

	idStr := strings.TrimSpace(*idFlag)

	// Handle array format: [id1,id2,id3] or ["id1","id2","id3"]
	if strings.HasPrefix(idStr, "[") && strings.HasSuffix(idStr, "]") {
		idStr = strings.Trim(idStr, "[]")
	}

	// Split by comma and clean up each ID
	var ids []string
	for _, id := range strings.Split(idStr, ",") {
		id = strings.TrimSpace(id)
		id = strings.Trim(id, `"'`) // Remove quotes if present
		if id != "" {
			ids = append(ids, id)
		}
	}

	return ids, nil
}

// buildQueryForSpecificIDs builds MongoDB query for specific property IDs
func buildQueryForSpecificIDs(ids []string) bson.M {
	if len(ids) == 1 {
		return bson.M{"_id": ids[0]}
	}
	return bson.M{"_id": bson.M{"$in": ids}}
}

// shouldProcessForDefault checks if document should be processed in default mode
func shouldProcessForDefault(doc bson.M, id string) (bool, error) {
	phoLH, hasPhoLH := doc["phoLH"]
	if !hasPhoLH || phoLH == nil {
		// No phoLH field or it's nil - should process
		golog.Debug("Processing document: no phoLH field", "_id", id)
		return true, nil
	}

	// Check if it's an array and get its length
	arrayLen := getPhoLHArrayLength(phoLH)
	if arrayLen == -1 {
		// phoLH exists but is not an array - skip it
		golog.Debug("Skipping document: phoLH is not an array", "_id", id, "phoLH_type", fmt.Sprintf("%T", phoLH))
		speedMeter.Check("skipped_non_array_phoLH", 1)
		return false, nil
	}

	// Process if array length is 0 (empty array should be processed)
	if arrayLen <= 0 {
		golog.Debug("Processing document: phoLH array is empty", "_id", id, "array_length", arrayLen)
		return true, nil
	}

	// Skip if array has elements
	golog.Debug("Skipping document: phoLH array has elements", "_id", id, "array_length", arrayLen)
	speedMeter.Check("skipped_has_phoLH", 1)
	return false, nil
}

// getPhoLHArrayLength returns the length of phoLH array, or -1 if not an array
func getPhoLHArrayLength(phoLH interface{}) int {
	if phoArray, ok := phoLH.(primitive.A); ok {
		return len(phoArray)
	}
	if phoArray, ok := phoLH.([]interface{}); ok {
		return len(phoArray)
	}
	return -1
}

// buildQuery constructs the MongoDB query based on command line parameters
func buildQuery() (bson.M, error) {
	// Step 1: Handle specific property IDs
	if *idFlag != "" {
		ids, err := parseSpecificIDs()
		if err != nil {
			return nil, fmt.Errorf("failed to parse specific IDs: %w", err)
		}
		query := buildQueryForSpecificIDs(ids)
		golog.Info("Built query for specific property IDs", "ids", ids, "count", len(ids))
		return query, nil
	}

	// Step 2: Handle custom query
	if *queryFlag != "" {
		queryStr := preprocessQuery(*queryFlag)
		var query bson.M
		err := bson.UnmarshalExtJSON([]byte(queryStr), true, &query)
		if err != nil {
			return nil, fmt.Errorf("failed to parse custom query JSON: %w", err)
		}
		golog.Info("Built custom query", "query", query)
		return query, nil
	}

	// Step 3: Handle phoNum mode - query all documents with phoUrls field
	if *phoNumFlag {
		query := bson.M{
			"phoUrls": bson.M{"$ne": nil},
		}
		golog.Info("Built query for phoNum mode - documents with phoUrls field")
		return query, nil
	}

	// Step 4: Default mode - query documents without phoLH or with empty phoLH
	query := bson.M{
		"$or": []bson.M{
			{"phoLH": bson.M{"$eq": nil}},
			{"phoLH": bson.M{"$size": 0}}, // empty array
		},
	}
	golog.Info("Built default query - documents without phoLH")
	return query, nil
}

// convertToBsonM converts various BSON types to bson.M
func convertToBsonM(item interface{}) (bson.M, error) {
	switch v := item.(type) {
	case bson.M:
		return v, nil
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			return nil, fmt.Errorf("failed to marshal bson.D: %w", err)
		}
		var doc bson.M
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			return nil, fmt.Errorf("failed to unmarshal to bson.M: %w", err)
		}
		golog.Debug("Successfully converted bson.D to bson.M")
		return doc, nil
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		return nil, fmt.Errorf("unsupported document type: %T", item)
	}
}

// extractDocumentID extracts the _id field from a document
func extractDocumentID(doc bson.M) (string, error) {
	if id, ok := doc["_id"].(string); ok {
		return id, nil
	}

	// Try ObjectID
	if objID, ok := doc["_id"].(primitive.ObjectID); ok {
		golog.Debug("ObjectID found, converting to hex", "objID", objID)
		return objID.Hex(), nil
	}

	golog.Error("Failed to extract _id field", "doc", doc)
	return "", fmt.Errorf("_id field not found or invalid type")
}

func processProperties(ctx context.Context) error {
	// Step 1: Parse command line flags
	flag.Parse()
	golog.Info("Starting AddToQueue batch processing", "dryrun", *dryrunFlag, "board", *boardFlag, "id", *idFlag, "query", *queryFlag, "phoNum", *phoNumFlag)

	// Step 2: Validate board flag
	collectionName, exists := goresodownload.BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Step 3: Initialize ResourceDownloadQueue
	golog.Info("Initializing ResourceDownloadQueue")
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	if queueCol == nil {
		return fmt.Errorf("failed to get queue collection: reso_photo_download_queue")
	}
	var err error
	queue, err = goresodownload.NewResourceDownloadQueue(queueCol)
	if err != nil {
		golog.Error("Failed to initialize ResourceDownloadQueue", "error", err)
		return fmt.Errorf("failed to initialize ResourceDownloadQueue: %v", err)
	}
	golog.Info("ResourceDownloadQueue initialized successfully")

	// Step 4: Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	if coll == nil {
		return fmt.Errorf("failed to get merged collection: %s", collectionName)
	}
	golog.Info("Processing collection", "board", *boardFlag, "collection", collectionName, "dryrun", *dryrunFlag)

	// Step 5: Build query based on command line parameters
	query, err := buildQuery()
	if err != nil {
		return fmt.Errorf("failed to build query: %w", err)
	}

	// Step 6: Execute query and get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	// Step 7: Configure streaming options
	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, item)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			fmt.Println("Total process time:", duration)
			// Check if error is not nil and has actual content
			if err != nil && err.Error() != "" {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	// Step 8: Start streaming processing
	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to add to download queue
func processItem(ctx context.Context, item interface{}) error {
	_ = ctx // Context not used in this function but required by gostreaming interface
	golog.Debug("Processing item started")

	// Step 1: Track processing speed
	speedMeter.Check("processed", 1)

	// Step 2: Convert item to bson.M (handle both bson.M and bson.D types)
	doc, err := convertToBsonM(item)
	if err != nil {
		speedMeter.Check("errors", 1)
		return fmt.Errorf("failed to convert document: %w", err)
	}

	// Step 3: Extract document ID
	id, err := extractDocumentID(doc)
	if err != nil {
		speedMeter.Check("idErrors", 1)
		return fmt.Errorf("failed to extract document ID: %w", err)
	}

	// Step 4: Determine processing mode and check if document should be processed
	mode := getProcessingMode()
	shouldProcess, err := shouldProcessDocument(doc, id, mode)
	if err != nil {
		golog.Error("Failed to check processing condition", "_id", id, "mode", mode, "error", err)
		speedMeter.Check("processingErrors", 1)
		return fmt.Errorf("failed to check processing condition: %w", err)
	}
	if !shouldProcess {
		return nil // Skip this document
	}

	golog.Debug("Document needs to be added to queue", "_id", id)

	// Step 5: Get priority for queue
	priority := getPriority(doc)
	if priority < 0 {
		golog.Error("Invalid priority value", "priority", priority, "propId", id)
		speedMeter.Check("priorityErrors", 1)
		return fmt.Errorf("invalid priority value: %d", priority)
	}

	// Step 6: Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run mode: Would add to queue",
			"_id", id,
			"priority", priority)
		return nil
	}

	// Step 7: Add to download queue
	err = queue.AddToQueue(id, priority, *boardFlag)
	if err != nil {
		golog.Error("Failed to add to queue",
			"_id", id,
			"board", *boardFlag,
			"error", err)
		speedMeter.Check("queueErrors", 1)
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	// Step 8: Track successful additions
	speedMeter.Check("added", 1)

	golog.Info("Successfully added to queue",
		"_id", id,
		"board", *boardFlag,
		"priority", priority)

	return nil
}

// getPriority calculates priority for a document
func getPriority(doc bson.M) int {
	// If processing specific IDs or custom query (from command line), always use priority 50000
	if *idFlag != "" || *queryFlag != "" {
		return 50000
	}

	// Use the goresodownload package's priority calculator for batch processing
	priority, err := goresodownload.CalculatePriority(*boardFlag, doc)
	if err != nil {
		golog.Debug("Failed to calculate priority, using default", "error", err)
		return 1000 // Default priority for batch processing
	}
	return priority
}

func main() {
	// Create context with timeout to prevent hanging
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	golog.Info("Starting AddToQueue batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("AddToQueue batch process completed successfully")
}
