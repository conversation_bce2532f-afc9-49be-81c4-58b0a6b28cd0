// Usage:
//   gen_file_server_dirs [flags]
//
// Flags:
//   -start int
//         Start date in YYYYMMDD format (e.g., 20150101) (default 20150101)
//   -end int
//         End date in YYYYMMDD format (e.g., 20151231) (default 20151231)
//
// Examples:
//   # Generate directories for default period (20150101-20151231)
//   gen_file_server_dirs
//
//   # Generate directories for a specific period
//   gen_file_server_dirs -start 20230101 -end 20231231
//
//   # Generate directories for a single month
//   gen_file_server_dirs -start 20240101 -end 20240131
//
// Note:
//   - Dates must be in YYYYMMDD format
//   - Start date must be >= 20150101
//   - End date must be >= start date and <= 99991231
//   - For USER board, directories are created yearly
//   - For other boards (TRB, DDF, etc.), directories are created weekly

// Package main provides a batch utility for generating directory structures
// for different boards in a file server system.
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"

	gobase "github.com/real-rm/gobase"
	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
)

// Command line flags
var (
	periodStartDate = flag.Int("start", 20150101, "Start date in YYYYMMDD format (e.g., 20150101)")
	periodEndDate   = flag.Int("end", 20151231, "End date in YYYYMMDD format (e.g., 20151231)")
)

// init initializes the application by loading configuration,
// setting up logging, and establishing MongoDB connection.
func init() {
	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB second (before any package that needs it)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Error("Failed to initialize MongoDB", "error", err)
	}

}

// MakeDirs creates a hierarchical directory structure for a board.
// It creates an L1 (level 1) directory and multiple L2 (level 2) directories
// under the specified base directory with proper permissions (0755).
func MakeDirs(baseDir string, l1 string, l2DirArray []string) error {
	// Create L1 directory
	l1Dir := filepath.Join(baseDir, l1)
	golog.Debug("Creating L1 directory", "dir", l1Dir)
	if err := os.MkdirAll(l1Dir, 0755); err != nil {
		golog.Error("Failed to create L1 dir", "dir", l1Dir, "error", err)
		return fmt.Errorf("failed to create L1 dir: %v", err)
	}

	// Create L2 directories
	for _, l2 := range l2DirArray {
		l2Dir := filepath.Join(l1Dir, l2)
		golog.Debug("Creating L2 directory", "dir", l2Dir)
		if err := os.MkdirAll(l2Dir, 0755); err != nil {
			golog.Error("Failed to create L2 dir", "dir", l2Dir, "error", err)
			return fmt.Errorf("failed to create L2 dir: %v", err)
		}
	}

	return nil
}

// main is the entry point of the program. It generates directory structures
// for different boards (TRB, DDF, BRE, etc.) between specified start and end dates.
// For each board, it:
// 1. Calculates L1 directories based on dates
// 2. Generates and stores L2 directories in MongoDB
// 3. Creates the physical directory structure in configured paths
// 4. Handles different date increment logic for USER vs other boards
func main() {
	flag.Parse()

	// Validate date inputs
	if *periodStartDate < 20150101 || *periodStartDate > 99991231 {
		golog.Error("Invalid start date", "date", *periodStartDate, "must be between 20150101 and 99991231")
		os.Exit(1)
	}
	if *periodEndDate < *periodStartDate || *periodEndDate > 99991231 {
		golog.Error("Invalid end date", "date", *periodEndDate, "must be between start date and 99991231")
		os.Exit(1)
	}

	// List of boards to generate directory structures for
	// boardTypes := []string{"TRB", "DDF", "BRE", "CLG", "OTW", "EDM", "CAR", "USER"}
	boardTypes := []string{"TRB", "USER"}

	// Iterate through each board and generate directory structure
	for _, boardType := range boardTypes {
		golog.Debug("Processing board", "board", boardType)

		// Start processing from the initial date
		processDate := *periodStartDate // 20150101

		// Get image directories from config
		configuredDirs, err := levelStore.GetImageDir(boardType)
		if err != nil {
			golog.Error("Failed to get image directories", "board", boardType, "error", err)
			continue
		}
		if len(configuredDirs) == 0 {
			golog.Error("No image directories configured", "board", boardType)
			continue
		}

		// Continue processing until we reach or exceed the end date
		// The loop processes dates differently based on board type:
		// - USER board: Processes yearly (increments by 1 year)
		// - Other boards: Processes weekly (increments by 7 days)
		for processDate <= *periodEndDate {
			// Calculate the L1 (level 1) directory name based on the date
			levelOneDirName := levelStore.CalculateL1(processDate, boardType)
			golog.Debug("Calculated L1 directory", "board", boardType, "l1", levelOneDirName)

			// Create a new directory key store for the current board
			dirKeyStore, err := levelStore.NewDirKeyStore(boardType, gomongo.Coll("vow", "file_server"))
			if err != nil {
				golog.Error("Failed to create dir key store",
					"board", boardType,
					"error", err)
				continue
			}
			// Generate and save L2 directories to MongoDB
			levelTwoDirs, err := dirKeyStore.SaveDirArrayInDB(context.Background(), boardType, processDate)
			if err != nil {
				golog.Error("Failed to generate dirs",
					"board", boardType,
					"l1", levelOneDirName,
					"error", err)
				continue
			}
			// Create physical directory structure in each configured path
			for _, storageBasePath := range configuredDirs {
				if err := MakeDirs(storageBasePath, levelOneDirName, levelTwoDirs); err != nil {
					golog.Error("Failed to create directories",
						"board", boardType,
						"baseDir", storageBasePath,
						"l1", levelOneDirName,
						"error", err)
					continue
				}
			}

			golog.Debug("Generated directory list",
				"board", boardType,
				"l1", levelOneDirName,
				"size", len(levelTwoDirs))

			// Convert to time.Time only when we need to do date arithmetic
			processTime := gohelper.DateToTime(processDate) // processTime: January 1, 2015 00:00:00.
			golog.Debug("Calculated process time", "board", boardType, "processTime", processTime)
			// Update the current date based on board type
			// USER board increments by year, others by week
			if boardType == "USER" {
				// For USER, increment by year
				processTime = processTime.AddDate(1, 0, 0)
			} else {
				// For other boards, increment by one week
				processTime = processTime.AddDate(0, 0, 7)
			}

			golog.Debug("Generation progress",
				"board", boardType,
				"current_date", processDate)

			// Convert back to date integer for next iteration
			processDate = gohelper.TimeToDateInt(processTime)
		}
	}

	golog.Info("Directory generation completed!")
}
