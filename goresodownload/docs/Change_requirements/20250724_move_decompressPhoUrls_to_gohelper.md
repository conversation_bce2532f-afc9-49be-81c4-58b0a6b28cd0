# 将 decompressPhoUrls 函数迁移到 gohelper 包

## 迁移目标

将 `decompressPhoUrls` 函数从 `addToQueue` 项目中提取到 `gohelper` 包中，使其成为可复用的压缩工具函数。

## 迁移内容

### 1. 创建新文件 `gohelper/helper_compression.go`

**选择理由：**
- 功能准确：专门处理压缩和解压缩相关的辅助函数
- 扩展性好：将来可以添加更多压缩算法（zlib, deflate, brotli等）
- 通用性强：不局限于 MongoDB，其他数据源也能使用
- 符合 gohelper 的命名规范：`helper_*.go`

**新增函数：**

#### `DecompressPhoUrls(phoUrls interface{}) (int, error)`
- **功能**：解压 gzip 压缩的 JSON 数组数据并返回数组长度
- **参数**：MongoDB primitive.Binary 包含 gzip 压缩的 JSON 数组
- **返回**：解压后 JSON 数组的长度和错误信息
- **用途**：专门用于处理压缩的数组数据（如照片 URL 数组）

#### `DecompressBinDataToJSON(binData primitive.Binary) (string, error)`
- **功能**：将 gzip 压缩的二进制数据解压为字符串
- **参数**：MongoDB primitive.Binary 包含 gzip 压缩的数据
- **返回**：解压后的数据字符串和错误信息
- **用途**：通用的 gzip 解压函数，适用于各种压缩数据

#### `CompressJSONToBinData(jsonStr string) (primitive.Binary, error)`
- **功能**：将字符串数据压缩为 MongoDB BinData 格式
- **参数**：要压缩的字符串数据（通常是 JSON，但可以是任何字符串）
- **返回**：gzip 压缩后的二进制数据，可直接存储到 MongoDB
- **用途**：创建压缩数据用于高效的 MongoDB 存储

### 2. 创建测试文件 `gohelper/helper_compression_test.go`

**测试覆盖：**
- ✅ 正常解压测试
- ✅ 空数组测试
- ✅ 无效类型测试
- ✅ 无效 gzip 数据测试
- ✅ 无效 JSON 测试
- ✅ 完整往返测试（JSON → BinData → JSON → 长度）
- ✅ 边界情况测试

**测试结果：**
```
=== RUN   TestDecompressPhoUrls
--- PASS: TestDecompressPhoUrls (0.02s)
=== RUN   TestDecompressPhoUrls_EmptyArray
--- PASS: TestDecompressPhoUrls_EmptyArray (0.03s)
=== RUN   TestDecompressPhoUrls_InvalidType
--- PASS: TestDecompressPhoUrls_InvalidType (0.00s)
=== RUN   TestDecompressPhoUrls_InvalidGzip
--- PASS: TestDecompressPhoUrls_InvalidGzip (0.00s)
=== RUN   TestDecompressPhoUrls_InvalidJSON
--- PASS: TestDecompressPhoUrls_InvalidJSON (0.01s)
```

### 3. 更新 addToQueue 项目

**修改内容：**
1. **添加 gohelper 导入**：
   ```go
   gohelper "github.com/real-rm/gohelper"
   ```

2. **移除本地函数**：
   - 删除了 `decompressPhoUrls` 函数的完整实现
   - 清理了不再需要的导入（bytes, compress/gzip, encoding/json, io）

3. **更新函数调用**：
   ```go
   // 修改前
   phoUrlsLength, err := decompressPhoUrls(phoUrls)
   
   // 修改后
   phoUrlsLength, err := gohelper.DecompressPhoUrls(phoUrls)
   ```

4. **配置本地依赖**：
   ```go
   // go.mod 中添加
   replace github.com/real-rm/gohelper => ../gohelper
   ```

## 迁移优势

### 1. 代码复用
- ✅ 其他项目可以直接使用 `gohelper.DecompressPhoUrls()`
- ✅ 避免重复实现相同功能
- ✅ 统一的压缩数据处理逻辑

### 2. 维护性提升
- ✅ 集中维护：压缩相关功能统一在 gohelper 包中
- ✅ 版本控制：通过包版本管理功能更新
- ✅ 测试覆盖：独立的测试确保功能稳定

### 3. 功能扩展
- ✅ 提供了更通用的 `DecompressBinDataToJSON` 函数
- ✅ 提供了反向操作 `CompressJSONToBinData` 函数
- ✅ 为将来添加更多压缩算法（zlib, deflate, brotli等）奠定基础

### 4. 代码清洁
- ✅ addToQueue 项目代码更简洁
- ✅ 减少了导入依赖
- ✅ 职责分离更清晰

## 使用示例

### 基本用法
```go
import gohelper "github.com/real-rm/gohelper"

// 解压 phoUrls 并获取长度
length, err := gohelper.DecompressPhoUrls(doc["phoUrls"])
if err != nil {
    log.Printf("Failed to decompress phoUrls: %v", err)
    return
}
fmt.Printf("Media array contains %d items\n", length)
```

### 通用解压
```go
// 解压任意 BinData 到 JSON
jsonStr, err := gohelper.DecompressBinDataToJSON(binData)
if err != nil {
    log.Printf("Failed to decompress: %v", err)
    return
}
```

### 压缩数据
```go
// 压缩 JSON 为 BinData
binData, err := gohelper.CompressJSONToBinData(jsonString)
if err != nil {
    log.Printf("Failed to compress: %v", err)
    return
}
```

## 向后兼容性

✅ **完全兼容**：addToQueue 的所有功能保持不变
✅ **性能一致**：使用相同的解压算法和逻辑
✅ **错误处理**：保持相同的错误处理方式
✅ **日志记录**：保持相同的日志输出格式

## 部署注意事项

1. **本地开发**：使用 `replace` 指令指向本地 gohelper 包
2. **生产环境**：需要将 gohelper 包推送到远程仓库并更新版本
3. **依赖管理**：确保所有使用项目都更新到包含新函数的 gohelper 版本

迁移成功提升了代码的复用性和可维护性，同时为其他项目提供了强大的 MongoDB 数据处理工具。
