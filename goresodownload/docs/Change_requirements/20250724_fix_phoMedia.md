# 需求 [fix_phoMedia]

## 反馈

1. ********* 图片url 404
2. DOWNLOAD_ALLOWED_MS 原来30s太短，会出现多次被处理
prodRni [direct: primary] rni>  db.reso_photo_download_queue.find({_id:'TRB*********'})
  {
    _id: 'TRB*********',
    _mt: ISODate('2025-07-24T16:35:34.216Z'),
    _ts: ISODate('2025-07-24T16:35:33.907Z'),
    dlShallEndTs: ISODate('2025-07-24T16:36:04.216Z'),
    priority: 50000,
    src: 'TRB'
  }
  [
  {
    _id: 'TRB*********',
    _mt: ISODate('2025-07-24T16:36:04.319Z'),
    _ts: ISODate('2025-07-24T16:35:33.907Z'),
    dlShallEndTs: ISODate('2025-07-24T16:36:34.319Z'),
    priority: 50000,
    src: 'TRB'
  }
]

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-24

## 原因
1.********* 处理queue的时候，显示没有media，获取到了当时replaceOne 没有media的情况。
也可能出现删除了一半图片的情况，需要batch处理

## 解决办法

### 1. 创建统一的扩展名映射函数
- 新增 `getFileExtensionFromMimeType()` 函数，统一处理 MIME 类型到文件扩展名的映射
- 支持所有常见的媒体类型：图像、文档、音频、视频、文本、归档等
- 遵循 DRY 原则，避免重复的扩展名映射逻辑

### 2. 修正扩展名映射逻辑
- 修改 `generateFileName()` 函数使用新的扩展名映射函数
- 修改 `generateHashList()` 函数使用新的扩展名映射函数
- 确保音频文件在 docLH 中显示正确的 `.mp3` 扩展名
- 为文档上下文添加特殊处理：无 MIME 类型时默认使用 `.pdf`

### 3. 支持的 MIME 类型
**图像类型**：
- `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/bmp`, `image/tiff`, `image/svg+xml`, `image/heic`, `image/vnd.microsoft.icon`
- 特殊处理：`immge/jpeg` (拼写错误), `jpeg` (简化格式)

**文档类型**：
- `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- `application/vnd.ms-excel`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- `application/rtf`, `text/rtf`, `application/vnd.oasis.opendocument.text`

**音频/视频类型**：
- `audio/mpeg` → `.mp3`
- `video/mp4` → `.mp4`, `video/quicktime` → `.mov`

**其他类型**：
- 文本、归档、二进制文件等

### 4. 增强 addToQueue 功能
- 支持 `-id` 参数：处理特定 ID 列表
- **新增 `-query` 参数**：支持自定义 MongoDB 查询条件
- 示例：`-query='{"docLH":{"$ne":null}}'` 查询有 docLH 且不为 null 的文档



## 是否需要补充UT

1. **已完成** - 添加了完整的单元测试：
   - `TestGetFileExtensionFromMimeType`：测试所有支持的 MIME 类型
   - `TestGenerateHashListWithMediaTypes`：测试不同媒体类型的哈希列表生成
   - 所有现有测试继续通过

## 确认日期:    2025-07-23


## online-step

1. 重启相关服务
   ```bash
   systemctl --user stop batch@goresodownloadTRB
   cd goresodownload
   make build
   cd rmconfig
   ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
   ```

2. 运行 batch，将之有docLH的文档添加到队列
   ```bash

   # 自定义查询：处理有 docLH 但没有 phoLH 的文档
   ./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{docLH:{\$ne:null}}' -dryrun"

   ```
】