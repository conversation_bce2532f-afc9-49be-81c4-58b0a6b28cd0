# addToQueue 代码重构说明

## 重构目标

优化 `processItem` 函数中过多的 if-else 嵌套逻辑，提高代码的可读性和可维护性。

## 重构前的问题

原来的 `processItem` 函数包含复杂的嵌套 if-else 逻辑：

```go
// 原来的复杂逻辑
if *phoNumFlag {
    // phoNum 模式逻辑
    shouldProcess, err := shouldProcessForPhoNum(doc, id)
    // ... 错误处理
} else if *idFlag == "" && *queryFlag == "" {
    // 默认模式逻辑
    phoLH, hasPhoLH := doc["phoLH"]
    if hasPhoLH && phoLH != nil {
        // 复杂的数组检查逻辑
        // ...
    }
}
// 特定 ID 或查询模式直接处理
```

## 重构后的改进

### 1. 引入处理模式枚举

```go
type ProcessingMode int

const (
    ModeDefault  ProcessingMode = iota // 默认模式：处理没有 phoLH 的文档
    ModePhoNum                         // phoNum 模式：检查 phoUrls vs phoLH 长度
    ModeSpecific                       // 特定模式：特定 ID 或自定义查询
)
```

### 2. 模式判断函数

```go
func getProcessingMode() ProcessingMode {
    if *phoNumFlag {
        return ModePhoNum
    }
    if *idFlag != "" || *queryFlag != "" {
        return ModeSpecific
    }
    return ModeDefault
}
```

### 3. 统一的文档处理判断

```go
func shouldProcessDocument(doc bson.M, id string, mode ProcessingMode) (bool, error) {
    switch mode {
    case ModePhoNum:
        return shouldProcessForPhoNum(doc, id)
    case ModeSpecific:
        return true, nil // 特定模式总是处理
    case ModeDefault:
        return shouldProcessForDefault(doc, id)
    default:
        return false, fmt.Errorf("unknown processing mode: %d", mode)
    }
}
```

### 4. 简化的 processItem 函数

```go
// 重构后的简洁逻辑
mode := getProcessingMode()
shouldProcess, err := shouldProcessDocument(doc, id, mode)
if err != nil {
    // 统一的错误处理
    return fmt.Errorf("failed to check processing condition: %w", err)
}
if !shouldProcess {
    return nil // 跳过文档
}
```

## 重构优势

### 1. 可读性提升
- 消除了深层嵌套的 if-else 结构
- 每个处理模式的逻辑独立且清晰
- 主流程逻辑更加直观

### 2. 可维护性提升
- 新增处理模式只需添加枚举值和对应的处理函数
- 每个模式的逻辑独立，修改不会影响其他模式
- 错误处理统一，便于调试

### 3. 可扩展性提升
- 添加新的处理模式变得简单
- 模式优先级在 `getProcessingMode` 中集中管理
- 每个模式的处理逻辑可以独立优化

### 4. 测试友好
- 每个函数职责单一，便于单元测试
- 模式判断逻辑可以独立测试
- 错误场景更容易模拟和测试

## 处理模式优先级

1. **ModePhoNum** (最高优先级)
   - 当 `-phoNum` 参数存在时
   - 检查 phoUrls 解压长度与 phoLH 长度

2. **ModeSpecific** (中等优先级)
   - 当 `-id` 或 `-query` 参数存在时
   - 处理特定 ID 或自定义查询的文档

3. **ModeDefault** (默认优先级)
   - 当没有其他参数时
   - 处理没有 phoLH 字段或 phoLH 为空的文档

## 向后兼容性

重构完全保持了向后兼容性：
- 所有现有的命令行参数和功能保持不变
- 处理逻辑的行为完全一致
- 只是代码结构更加清晰和易于维护

## 测试验证

通过测试验证了所有处理模式的正确性：
- ✅ 默认模式
- ✅ PhoNum 模式  
- ✅ 特定 ID 模式
- ✅ 自定义查询模式
- ✅ 模式优先级正确性

重构成功提升了代码质量，同时保持了功能的完整性和稳定性。
