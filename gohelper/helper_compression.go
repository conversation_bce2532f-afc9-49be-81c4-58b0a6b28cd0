// Package gohelper provides compression and decompression utilities.
// This file contains functions for handling gzip-compressed data,
// particularly for MongoDB BinData storage and retrieval.
package gohelper

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"

	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DecompressPhoUrls decompresses gzip-compressed JSON array data and returns the array length.
// This function is designed to handle compressed JSON array data stored as MongoDB BinData,
// specifically for photo URL arrays, but can be used for any JSON array data.
//
// Parameters:
//   - phoUrls: MongoDB primitive.Binary containing gzip-compressed JSON array
//
// Returns:
//   - int: Length of the decompressed JSON array
//   - error: Any error that occurred during decompression or parsing
//
// Example usage:
//
//	length, err := gohelper.DecompressPhoUrls(doc["phoUrls"])
//	if err != nil {
//	    log.Printf("Failed to decompress data: %v", err)
//	    return
//	}
//	fmt.Printf("Array contains %d items\n", length)
func DecompressPhoUrls(phoUrls interface{}) (int, error) {
	// Handle primitive.Binary type from MongoDB
	var binData primitive.Binary
	var ok bool

	if binData, ok = phoUrls.(primitive.Binary); !ok {
		return 0, fmt.Errorf("phoUrls is not a primitive.Binary type, got %T", phoUrls)
	}

	// Create a gzip reader from the binary data
	reader, err := gzip.NewReader(bytes.NewReader(binData.Data))
	if err != nil {
		return 0, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer func() {
		if err := reader.Close(); err != nil {
			golog.Error("Failed to close gzip reader", "error", err)
		}
	}()

	// Read all decompressed data
	decompressed, err := io.ReadAll(reader)
	if err != nil {
		return 0, fmt.Errorf("failed to read decompressed data: %w", err)
	}

	// Parse JSON to get the media array
	var mediaArray []interface{}
	if err := json.Unmarshal(decompressed, &mediaArray); err != nil {
		return 0, fmt.Errorf("failed to parse decompressed JSON: %w", err)
	}

	return len(mediaArray), nil
}

// DecompressBinDataToJSON decompresses gzip-compressed binary data to JSON string.
// This is a general-purpose function for decompressing any gzip-compressed data
// stored as MongoDB BinData.
//
// Parameters:
//   - binData: MongoDB primitive.Binary containing gzip-compressed data
//
// Returns:
//   - string: Decompressed data as string
//   - error: Any error that occurred during decompression
func DecompressBinDataToJSON(binData primitive.Binary) (string, error) {
	// Create a gzip reader from the binary data
	reader, err := gzip.NewReader(bytes.NewReader(binData.Data))
	if err != nil {
		return "", fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer func() {
		if err := reader.Close(); err != nil {
			golog.Error("Failed to close gzip reader", "error", err)
		}
	}()

	// Read all decompressed data
	decompressed, err := io.ReadAll(reader)
	if err != nil {
		return "", fmt.Errorf("failed to read decompressed data: %w", err)
	}

	return string(decompressed), nil
}

// CompressJSONToBinData compresses string data to MongoDB BinData format using gzip.
// This function creates gzip-compressed binary data suitable for efficient storage
// in MongoDB as BinData.
//
// Parameters:
//   - jsonStr: String data to compress (typically JSON, but can be any string)
//
// Returns:
//   - primitive.Binary: Gzip-compressed binary data ready for MongoDB storage
//   - error: Any error that occurred during compression
func CompressJSONToBinData(jsonStr string) (primitive.Binary, error) {
	var buf bytes.Buffer

	// Create gzip writer
	gzipWriter := gzip.NewWriter(&buf)

	// Write JSON data
	if _, err := gzipWriter.Write([]byte(jsonStr)); err != nil {
		return primitive.Binary{}, fmt.Errorf("failed to write to gzip writer: %w", err)
	}

	// Close writer to flush data
	if err := gzipWriter.Close(); err != nil {
		return primitive.Binary{}, fmt.Errorf("failed to close gzip writer: %w", err)
	}

	// Return as primitive.Binary
	return primitive.Binary{
		Subtype: 2, // Generic binary subtype
		Data:    buf.Bytes(),
	}, nil
}
