package gohelper

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestDecompressPhoUrls(t *testing.T) {
	// Create test data - a JSON array with 3 media items
	testData := []map[string]interface{}{
		{"id": 1, "url": "http://example.com/1.jpg", "type": "photo"},
		{"id": 2, "url": "http://example.com/2.jpg", "type": "photo"},
		{"id": 3, "url": "http://example.com/3.pdf", "type": "document"},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(testData)
	require.NoError(t, err)

	// Compress to BinData
	binData, err := CompressJSONToBinData(string(jsonData))
	require.NoError(t, err)

	// Test the function
	length, err := DecompressPhoUrls(binData)
	require.NoError(t, err)
	assert.Equal(t, 3, length)
}

func TestDecompressPhoUrls_EmptyArray(t *testing.T) {
	// Test with empty array
	testData := []interface{}{}
	jsonData, err := json.Marshal(testData)
	require.NoError(t, err)

	binData, err := CompressJSONToBinData(string(jsonData))
	require.NoError(t, err)

	length, err := DecompressPhoUrls(binData)
	require.NoError(t, err)
	assert.Equal(t, 0, length)
}

func TestDecompressPhoUrls_InvalidType(t *testing.T) {
	// Test with invalid type
	_, err := DecompressPhoUrls("not a binary")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "phoUrls is not a primitive.Binary type")
}

func TestDecompressPhoUrls_InvalidGzip(t *testing.T) {
	// Test with invalid gzip data
	binData := primitive.Binary{
		Subtype: 2,
		Data:    []byte("invalid gzip data"),
	}

	_, err := DecompressPhoUrls(binData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create gzip reader")
}

func TestDecompressPhoUrls_InvalidJSON(t *testing.T) {
	// Create invalid JSON data and compress it
	invalidJSON := "invalid json"
	binData, err := CompressJSONToBinData(invalidJSON)
	require.NoError(t, err)

	_, err = DecompressPhoUrls(binData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse decompressed JSON")
}

func TestDecompressBinDataToJSON(t *testing.T) {
	// Test data
	originalJSON := `{"test": "data", "number": 123}`
	
	// Compress
	binData, err := CompressJSONToBinData(originalJSON)
	require.NoError(t, err)
	
	// Decompress
	decompressedJSON, err := DecompressBinDataToJSON(binData)
	require.NoError(t, err)
	
	assert.Equal(t, originalJSON, decompressedJSON)
}

func TestCompressJSONToBinData(t *testing.T) {
	// Test data
	testJSON := `{"items": [1, 2, 3], "name": "test"}`
	
	// Compress
	binData, err := CompressJSONToBinData(testJSON)
	require.NoError(t, err)
	
	// Verify it's a valid binary
	assert.Equal(t, byte(2), binData.Subtype)
	assert.NotEmpty(t, binData.Data)
	
	// Verify we can decompress it back
	decompressed, err := DecompressBinDataToJSON(binData)
	require.NoError(t, err)
	assert.Equal(t, testJSON, decompressed)
}

func TestRoundTrip(t *testing.T) {
	// Test complete round trip: JSON -> BinData -> JSON -> Array Length
	testData := []string{"item1", "item2", "item3", "item4", "item5"}
	
	// Step 1: Marshal to JSON
	jsonData, err := json.Marshal(testData)
	require.NoError(t, err)
	
	// Step 2: Compress to BinData
	binData, err := CompressJSONToBinData(string(jsonData))
	require.NoError(t, err)
	
	// Step 3: Decompress and get array length
	length, err := DecompressPhoUrls(binData)
	require.NoError(t, err)
	
	// Verify
	assert.Equal(t, 5, length)
	assert.Equal(t, len(testData), length)
}

func TestDecompressBinDataToJSON_InvalidGzip(t *testing.T) {
	// Test with invalid gzip data
	binData := primitive.Binary{
		Subtype: 2,
		Data:    []byte("not gzip data"),
	}
	
	_, err := DecompressBinDataToJSON(binData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create gzip reader")
}

func TestCompressJSONToBinData_EmptyString(t *testing.T) {
	// Test with empty string
	binData, err := CompressJSONToBinData("")
	require.NoError(t, err)
	
	// Should be able to decompress back to empty string
	decompressed, err := DecompressBinDataToJSON(binData)
	require.NoError(t, err)
	assert.Equal(t, "", decompressed)
}
